package com.zsm.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * 接口同步账号枚举
 * 用于管理多个药房账号的用户名和密码
 */
@Getter
@AllArgsConstructor
public enum SyncAccountEnum {
    /**
     * 静配中心
     */
    TAIHE_IV_CENTER("", "","11", "静配中心"),

    /**
     * 手术麻醉药房
     */
    TAIHE_SURGERY_ANESTHESIA_PHARMACY("", "","24", "手术麻醉药房"),
    /**
     * 住院药房
     */
    TAIHE_INPATIENT_PHARMACY("", "","3", "本部住院药房");

    /**
     * 账号
     */
    private final String username;
    
    /**
     * 密码
     */
    private final String password;
    /**
     * 药房id
     */
    private final String fyyf;
    /**
     * 描述
     */
    private final String description;

    /**
     * 获取所有账号枚举值的列表
     * @return 所有枚举值的列表
     */
    public static List<SyncAccountEnum> getAllAccounts() {
        return Arrays.asList(values());
    }

    /**
     * 遍历所有账号并执行指定操作
     * @param accountProcessor 账号处理器函数式接口
     */
    public static void forEachAccount(AccountProcessor accountProcessor) {
        for (SyncAccountEnum account : values()) {
            accountProcessor.process(account);
        }
    }

    /**
     * 根据用户名查找账号
     * @param username 用户名
     * @return 匹配的账号枚举，如果未找到返回null
     */
    public static SyncAccountEnum findByUsername(String username) {
        for (SyncAccountEnum account : values()) {
            if (account.username.equals(username)) {
                return account;
            }
        }
        return null;
    }

    /**
     * 根据描述查找账号
     * @param description 描述
     * @return 匹配的账号枚举，如果未找到返回null
     */
    public static SyncAccountEnum findByDescription(String description) {
        for (SyncAccountEnum account : values()) {
            if (account.description.equals(description)) {
                return account;
            }
        }
        return null;
    }

    /**
     * 根据发药药房ID(fyyf)查找账号
     * @param fyyf 发药药房ID
     * @return 匹配的账号枚举，如果未找到返回null
     */
    public static SyncAccountEnum findByFyyf(String fyyf) {
        for (SyncAccountEnum account : values()) {
            if (account.fyyf.equals(fyyf)) {
                return account;
            }
        }
        return null;
    }

    /**
     * 账号处理器函数式接口
     */
    @FunctionalInterface
    public interface AccountProcessor {
        /**
         * 处理账号
         * @param account 账号枚举
         */
        void process(SyncAccountEnum account);
    }
} 
