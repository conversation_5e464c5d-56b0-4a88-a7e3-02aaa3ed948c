#!/usr/bin/bash

# 应用管理脚本
# 用法: ./run.sh {start|stop|restart|status}

# 配置变量
APP_NAME="dingYuanZongYuan"
JAR_NAME="${APP_NAME}-2.1.jar"
JAR_PATH="./${JAR_NAME}"
PID_FILE="./app.pid"
LOG_FILE="./app.log"
JVM_OPTS="-Xms2048m -Xmx10240m -XX:+HeapDumpOnOutOfMemoryError -XX:+UseG1GC -XX:MaxGCPauseMillis=200"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 检查jar包是否存在
check_jar_exists() {
    if [ ! -f "$JAR_PATH" ]; then
        log_error "JAR包不存在: $JAR_PATH"
        log_info "请先执行 'mvn clean package' 构建项目"
        exit 1
    fi
}

# 获取应用PID
get_app_pid() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        # 验证PID是否有效
        if ps -p $PID > /dev/null 2>&1; then
            echo $PID
        else
            # PID文件存在但进程不存在，删除PID文件
            rm -f "$PID_FILE"
            echo ""
        fi
    else
        # 通过进程名查找
        PID=$(ps -ef | grep "$JAR_NAME" | grep -v grep | awk '{print $2}')
        echo $PID
    fi
}

# 检查应用状态
check_status() {
    PID=$(get_app_pid)
    if [ -n "$PID" ]; then
        log_info "应用正在运行，PID: $PID"
        return 0
    else
        log_warn "应用未运行"
        return 1
    fi
}

# 启动应用
start_app() {
    log_info "正在启动应用..."
    
    # 检查jar包是否存在
    check_jar_exists
    
    # 检查应用是否已经在运行
    if check_status > /dev/null 2>&1; then
        log_warn "应用已经在运行中"
        return 0
    fi
    
    # 创建日志目录
    mkdir -p logs
    
    # 启动应用
    log_info "执行启动命令: nohup java $JVM_OPTS -jar $JAR_PATH"
    nohup java $JVM_OPTS -jar "$JAR_PATH" > "$LOG_FILE" 2>&1 &
    
    # 保存PID
    echo $! > "$PID_FILE"
    
    # 等待应用启动
    sleep 3
    
    # 验证启动状态
    if check_status > /dev/null 2>&1; then
        log_info "应用启动成功！"
        log_info "日志文件: $LOG_FILE"
        log_info "PID文件: $PID_FILE"
    else
        log_error "应用启动失败，请检查日志: $LOG_FILE"
        exit 1
    fi
}

# 停止应用
stop_app() {
    log_info "正在停止应用..."
    
    PID=$(get_app_pid)
    if [ -z "$PID" ]; then
        log_warn "应用未运行"
        return 0
    fi
    
    log_info "找到应用进程，PID: $PID"
    
    # 优雅停止
    log_info "尝试优雅停止应用..."
    kill -TERM $PID
    
    # 等待进程结束
    for i in {1..10}; do
        if ! ps -p $PID > /dev/null 2>&1; then
            log_info "应用已优雅停止"
            break
        fi
        log_debug "等待应用停止... ($i/10)"
        sleep 1
    done
    
    # 如果进程仍在运行，强制停止
    if ps -p $PID > /dev/null 2>&1; then
        log_warn "应用未能优雅停止，强制停止..."
        kill -9 $PID
        sleep 1
        
        if ps -p $PID > /dev/null 2>&1; then
            log_error "无法停止应用，PID: $PID"
            exit 1
        else
            log_info "应用已强制停止"
        fi
    fi
    
    # 清理PID文件
    rm -f "$PID_FILE"
    log_info "应用停止完成"
}

# 重启应用
restart_app() {
    log_info "正在重启应用..."
    stop_app
    sleep 2
    start_app
}

# 显示帮助信息
show_help() {
    echo "应用管理脚本"
    echo ""
    echo "用法: $0 {start|stop|restart|status|help} 或使用短命令 {s|t|r|st|h}"
    echo ""
    echo "命令说明:"
    echo "  start   (s)  - 启动应用"
    echo "  stop    (t)  - 停止应用"
    echo "  restart (r)  - 重启应用"
    echo "  status  (st) - 查看应用状态"
    echo "  help    (h)  - 显示此帮助信息"
    echo ""
    echo "配置信息:"
    echo "  应用名称: $APP_NAME"
    echo "  JAR文件: $JAR_PATH"
    echo "  日志文件: $LOG_FILE"
    echo "  PID文件: $PID_FILE"
    echo "  JVM参数: $JVM_OPTS"
}

# 主函数
main() {
    case "$1" in
        start|s)
            start_app
            ;;
        stop|t)
            stop_app
            ;;
        restart|r)
            restart_app
            ;;
        status|st)
            check_status
            ;;
        help|--help|-h|h)
            show_help
            ;;
        *)
            log_error "无效的命令: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 检查参数
if [ $# -eq 0 ]; then
    log_error "缺少命令参数"
    echo ""
    show_help
    exit 1
fi

# 执行主函数
main "$1" 
